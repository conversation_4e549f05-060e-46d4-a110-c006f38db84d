import 'dart:io';
import 'dart:ui';
import 'package:boutigak/constants/app_colors.dart';
import 'package:boutigak/controllers/item_controller.dart';
import 'package:boutigak/controllers/sell_controller.dart';
import 'package:boutigak/controllers/store_controller.dart';
import 'package:boutigak/data/models/categories.dart';
import 'package:boutigak/data/models/item.dart';
import 'package:boutigak/data/models/store.dart';
import 'package:boutigak/data/services/webservices.dart';
import 'package:boutigak/utils/color_utils.dart';
import 'package:boutigak/views/boutigat_store/orderdeatails_page.dart';
import 'package:boutigak/views/profil/boutigak/boutigak_user_page.dart';
import 'package:boutigak/views/sell/sell_widgets.dart';
import 'package:boutigak/views/widgets/boutigak_loading_widget.dart';
import 'package:boutigak/views/widgets/customappbar_widget.dart';
import 'package:boutigak/views/widgets/custombutton_widget.dart';
import 'package:boutigak/views/widgets/image_slider_widget.dart';
import 'package:boutigak/views/widgets/upload_progress_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import 'package:palette_generator/palette_generator.dart';


class StoreProductPage extends StatefulWidget {
  @override
  _StoreProductPageState createState() => _StoreProductPageState();
}

class _StoreProductPageState extends State<StoreProductPage> {
  final StoreController storeController = Get.put(StoreController());
  final double expandedHeight = 300.0;

  Color? dominantColor = AppColors.surface;
  Color? surfaceColor = AppColors.surface;
  String? currentImageUrl;

  late final ItemController itemController;


  @override
  void initState() {
    super.initState();

    // Init contrôleurs comme dans StoreDetailsPage
    itemController = Get.put(ItemController());


    // Charger favoris & items
    Future.wait([
      storeController.fetchMyFavoriteCategories(),
      storeController.fetchMyStoreItems(),
    ]).then((_) {
      storeController.selectAllCategories();
    });

    // Charger l'image et couleur dominante
    _initializeStore();
  }

  Future<void> _initializeStore() async {
    await storeController.fetchedMyStoreInformation();

    final store = storeController.myStore.value;
    if (store != null && store.images.isNotEmpty) {
      currentImageUrl = store.images.first;
      await _updateDominantColor(currentImageUrl!);
    }
  }

  bool isDarkColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance < 0.5;
  }

  Future<void> _updateDominantColor(String imageUrl) async {
    final color = await getMostFrequentColor(imageUrl);
    setState(() {
      dominantColor = color;
      surfaceColor = isDarkColor(color) ? Colors.white : Colors.black;
    });
  }

  @override
  Widget build(BuildContext context) {
    final store = storeController.myStore.value;

    return Scaffold(
      body: store != null && store.images.isNotEmpty
          ? NestedScrollView(
              headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    expandedHeight: expandedHeight,
                    pinned: true,
                    floating: true,
                    elevation: 0,
                    backgroundColor: dominantColor ?? AppColors.primary,
                    flexibleSpace: FlexibleSpaceBar(
                      collapseMode: CollapseMode.pin,
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          Image.network('${store.images.first}', fit: BoxFit.cover),
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withOpacity(0.1),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {},
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          foregroundColor: AppColors.surface,
                        ),
                        child: Text("${store.followersCount} Followers"),
                      ),
                     
                    ],
                  ),
                  SliverPersistentHeader(
                    delegate: _SliverAppBarDelegate(
                      child: FavoriteCategoryListWidget(storeId: store.id!),
                      height: 62,
                    ),
                    pinned: true,
                  ),
                ];
              },
              body: Container(
                color: const Color(0xFFf2f3f5),
                child: Column(
                  children: [
                    Expanded(
                      child: Obx(() {
                        final selectedCat = storeController.selectedCategory.value;
                        if (selectedCat == null) {
                          return MyStoreitemsListViewWidget(
                            category: null,
                            items: storeController.myItems,
                            storeImage: store.images.first,
                          );
                        } else {
                          final filteredItems = storeController.myItems
                              .where((item) => item.categoryId == selectedCat.id)
                              .toList();
                          return MyStoreitemsListViewWidget(
                            category: selectedCat,
                            items: filteredItems,
                            storeImage: store.images.first,
                          );
                        }
                      }),
                    ),
                  ],
                ),
              ),
            )
          : const Center(child: CircularProgressIndicator()),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Get.to(() => SellStorePage())?.then((_) async {
            // Recharger store et couleur après ajout
            await storeController.fetchedMyStoreInformation();
            final updatedStore = storeController.myStore.value;
            if (updatedStore != null && updatedStore.images.isNotEmpty) {
              await _updateDominantColor(updatedStore.images.first);
            }

            await storeController.fetchMyStoreItems();
            storeController.selectAllCategories();
          });
        },
        backgroundColor: dominantColor,
        tooltip: 'Ajouter un item',
        child: Icon(Icons.add, color: surfaceColor),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}



class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double height;

  _SliverAppBarDelegate({required this.child, required this.height});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => height;

  @override
  double get minExtent => height;

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return oldDelegate.child != child || oldDelegate.height != height;
  }
}











class FavoriteCategoryListWidget extends StatefulWidget {
  final int storeId;

  const FavoriteCategoryListWidget({
    Key? key,
    required this.storeId,
  }) : super(key: key);

  @override
  _FavoriteCategoryListWidgetState createState() => _FavoriteCategoryListWidgetState();
}

class _FavoriteCategoryListWidgetState extends State<FavoriteCategoryListWidget> {
  final StoreController storeController = Get.find<StoreController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      storeController.fetchStoreFavoriteCategories(widget.storeId.toString());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      color: Theme.of(context).colorScheme.surface,
      child: Obx(() {
        final categories = storeController.FavoriteCategories;

        return ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: categories.length + 2, // +1 for "All", +1 for "+"
          itemBuilder: (context, index) {
            // 🟡 Bouton "All" au début
            if (index == 0) {
              final isSelected = storeController.selectedCategory.value == null;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: TextButton(
                  onPressed: () {
                    storeController.selectAllCategories();
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.onSurface,
                  ),
                  child: Text(
                    'All',
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              );
            }

            // 🟡 Dernier = bouton "+"
            if (index == categories.length + 1) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Container(
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.add, color: Colors.black),
                    onPressed: () {
                      Get.to(() => CategorySelectionPage(controller: storeController));
                    },
                  ),
                ),
              );
            }

            // 🟡 Catégorie favorite
            final category = categories[index - 1]; // -1 à cause de "All"
            final isSelected = storeController.selectedCategory.value == category;

            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextButton(
                onPressed: () {
                  storeController.selectCategory(
                    category,
                    widget.storeId.toString(),
                  );
                },
                onLongPress: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('Supprimer la catégorie'),
                        content: const Text(
                            'Êtes-vous sûr de vouloir supprimer cette catégorie de vos favoris ?'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Annuler'),
                          ),
                          TextButton(
                            onPressed: () async {
                              Navigator.of(context).pop();
                              storeController.deleteStoreFavoriteCategory(category.id);
                              setState(() {});
                            },
                            child: const Text('Supprimer', style: TextStyle(color: Colors.red)),
                          ),
                        ],
                      );
                    },
                  );
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                ),
                child: Text(
                  category.getTitle(),
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}



class SellStorePage extends StatefulWidget {
  final int? itemId;

  const SellStorePage({Key? key, this.itemId}) : super(key: key);

  @override
  _SellStorePageState createState() => _SellStorePageState();
}

class _SellStorePageState extends State<SellStorePage> {
  final ItemController itemController = Get.put(ItemController(), permanent: true);
  final PhotoActionsController photoController =
      Get.put(PhotoActionsController(Get.find<ItemController>()), permanent: true);

  final RxString uploadStatus = "Preparing upload...".obs;
  final RxDouble uploadProgress = 0.0.obs;

  @override
  void initState() {
    super.initState();
    if (widget.itemId != null) {
      itemController.loadItemForEdit(widget.itemId!);
    }
  }

  @override
  void dispose() {
    itemController.clearItemData();
    photoController.clearPhotoActionData();
    itemController.clearFields();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double sidePadding = screenWidth * 0.0407;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Get.back(),
        ),
        title: Text(
          "add item to your store",
          style: TextStyle(
            color: Theme.of(context).colorScheme.surface,
            fontSize: AppTextSizes.heading,
          ),
        ),
        elevation: 0,
        backgroundColor: AppColors.primary,
      ),
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Show loader if we're in edit mode and still loading the item
            Obx(() {
              if (widget.itemId != null && itemController.isLoadingItemForEdit.value) {
                return Container(
                  height: MediaQuery.of(context).size.height * 0.6,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading item...'),
                      ],
                    ),
                  ),
                );
              }
              return SizedBox.shrink();
            }),

            // Main content - only show when not loading
            Obx(() {
              if (widget.itemId != null && itemController.isLoadingItemForEdit.value) {
                return SizedBox.shrink();
              }

              return Column(
                children: [
                  PhotoActionsWidget(),
                  SizedBox(height: 20),
                  ItemDetailsEntryMainLanguage(),
                  SizedBox(height: 20),
                  ItemDetailsEntryArabic(),
                  SizedBox(height: 20),
                  ItemDetailsFormWidget(),
                ],
              );
            }),

            // Category details section
            Obx(() {
              if (itemController.selectedCategoryDetails.isEmpty) {
                return const SizedBox.shrink();
              }
              return CategoryDetailsWidget();
            }),

            // Upload progress and button section
            Obx(() {
                return Column(
                  children: [
                    if (uploadProgress.value > 0)
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                        child: UploadProgressWidget(
                          progress: uploadProgress.value,
                          status: uploadStatus.value,
                          isComplete: uploadProgress.value >= 1.0,
                        ),
                      ),
                    SizedBox(height: 10),
                    CustomButton(
                      text: widget.itemId != null ? "update_item".tr : "upload_item".tr,
                      onPressed: () async {
                        uploadProgress.value = 0.0;
                        uploadStatus.value = "Preparing upload...";

                        if (widget.itemId != null) {
                          // Check if this is a store item by looking at the selected item
                          bool isStoreItem = itemController.selectedItem.value?.storeId != null;
                          
                          if (isStoreItem) {
                            await itemController.updateStoreItem(widget.itemId!);
                          } else {
                            await itemController.updateItem(widget.itemId!);
                          }
                          
                          itemController.clearItemData();
                          photoController.clearPhotoActionData();
                          itemController.clearFields();

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text("item_updated_successfully".tr),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                          await Future.delayed(Duration(seconds: 1));
                          uploadProgress.value = 0.0;
                          uploadStatus.value = "";
                          Get.find<StoreController>().fetchMyStoreItems();
                          Get.back();
                        } else {
                          try {
                            uploadProgress.value = 0.25;
                            uploadStatus.value = "Validating data...";
                            await Future.delayed(Duration(milliseconds: 500));

                            uploadProgress.value = 0.5;
                            uploadStatus.value = "Uploading images...";

                            uploadProgress.value = 0.75;
                            uploadStatus.value = "Creating item...";

                            await itemController.postStoreItemWithProgress((progress) {
                              uploadProgress.value = 0.75 + (progress * 0.25);
                            });

                            uploadProgress.value = 1.0;
                            uploadStatus.value = "Upload complete!";

                            if (itemController.isItemUploaded.value) {
                              // Show success message
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text("item_uploaded_successfully".tr),
                                    backgroundColor: Colors.green,
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }

                              // Wait for success animation to complete
                              await Future.delayed(Duration(seconds: 2));

                              // Clear form data
                              itemController.clearItemData();
                              photoController.clearPhotoActionData();
                              itemController.clearFields();

                              // Reset progress
                              uploadProgress.value = 0.0;
                              uploadStatus.value = "";

                              // Refresh store items and navigate back
                              Get.find<StoreController>().fetchMyStoreItems();
                              Get.back();
                            }
                          } catch (e) {
                            uploadStatus.value = "Upload failed. Please try again.";
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text("Error uploading item: ${e.toString()}"),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        }
                      },
                    ),
                  ],
                );
              }),
              SizedBox(height: 30),
            ],
          ),
      ),
    );
  }
}

class CategorySelectionPage extends StatelessWidget {
  final StoreController controller;
  final Category? parentCategory;

  const CategorySelectionPage({
    Key? key,
    required this.controller,
    this.parentCategory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (parentCategory == null) {
      controller.fetchCategories();
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(parentCategory == null
            ? 'Select Category'
            : 'Select ${parentCategory!.getTitle()} Subcategory'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop();
            if (parentCategory != null) Navigator.of(context).pop();
          },
        ),
      ),
      body: Obx(() {
        final List<Category> categories = parentCategory == null
            ? controller.categories.where((c) => c.parentId == null).toList()
            : parentCategory!.children;

        if (controller.isLoading.value) {
          return Center(
            child: LottieLoopingLoadingWidget(
              animationPath: 'assets/lottie/Boutigaklogo.json',
            ),
          );
        }

        if (categories.isEmpty) {
          return const Center(child: Text('No categories available.'));
        }

        return ListView.separated(
          itemCount: categories.length,
          separatorBuilder: (_, __) => const Divider(height: 1),
          itemBuilder: (context, index) {
            final category = categories[index];

            return ListTile(
              title: Text(category.getTitle()),
              trailing: category.children.isNotEmpty
                  ? const Icon(Icons.arrow_forward_ios)
                  : const Icon(Icons.radio_button_unchecked),
              onTap: () {
                if (category.children.isNotEmpty) {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (context) => FractionallySizedBox(
                      heightFactor: 0.95,
                      child: CategorySelectionPage(
                        controller: controller,
                        parentCategory: category,
                      ),
                    ),
                  );
                } else {
                  controller.addFavoriteCategory(category.id);
                  Navigator.of(context).pop();
                  if (parentCategory != null) Navigator.of(context).pop();
                }
              },
            );
          },
        );
      }),
    );
  }
}
